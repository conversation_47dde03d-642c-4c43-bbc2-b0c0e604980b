

#include "main.h"
#include "screen.h"
#include "file_op.h"
#include "ff.h"
#include "sysoper.h"
#include "w25qxx.h"




uint8_t UI=0;
uint8_t  up,down;
uint8_t  dot_chr[5]={0};
uint8_t  cu[4]={0};

uint8_t  do_ch[5]={0};

const uint8_t FON_asc16[1536] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00, 
	0x00, 0x12, 0x36, 0x24, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x24, 0x24, 0x24, 0xFE, 0x48, 0x48, 0x48, 0xFE, 0x48, 0x48, 0x48, 0x00, 0x00, 
	0x00, 0x00, 0x10, 0x38, 0x54, 0x54, 0x50, 0x30, 0x18, 0x14, 0x14, 0x54, 0x54, 0x38, 0x10, 0x10, 
	0x00, 0x00, 0x00, 0x44, 0xA4, 0xA8, 0xA8, 0xA8, 0x54, 0x1A, 0x2A, 0x2A, 0x2A, 0x44, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x30, 0x48, 0x48, 0x48, 0x50, 0x6E, 0xA4, 0x94, 0x88, 0x89, 0x76, 0x00, 0x00, 
	0x00, 0x60, 0x60, 0x20, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x02, 0x04, 0x08, 0x08, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x08, 0x08, 0x04, 0x02, 0x00, 
	0x00, 0x40, 0x20, 0x10, 0x10, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x10, 0x10, 0x20, 0x40, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0xD6, 0x38, 0x38, 0xD6, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0x10, 0x10, 0xFE, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x60, 0x20, 0xC0, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x60, 0x00, 0x00, 
	0x00, 0x00, 0x01, 0x02, 0x02, 0x04, 0x04, 0x08, 0x08, 0x10, 0x10, 0x20, 0x20, 0x40, 0x40, 0x00, 
	0x00, 0x00, 0x00, 0x18, 0x24, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x24, 0x18, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x10, 0x70, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x7C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x42, 0x04, 0x04, 0x08, 0x10, 0x20, 0x42, 0x7E, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x04, 0x18, 0x04, 0x02, 0x02, 0x42, 0x44, 0x38, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x04, 0x0C, 0x14, 0x24, 0x24, 0x44, 0x44, 0x7E, 0x04, 0x04, 0x1E, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x7E, 0x40, 0x40, 0x40, 0x58, 0x64, 0x02, 0x02, 0x42, 0x44, 0x38, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x1C, 0x24, 0x40, 0x40, 0x58, 0x64, 0x42, 0x42, 0x42, 0x24, 0x18, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x7E, 0x44, 0x44, 0x08, 0x08, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x42, 0x24, 0x18, 0x24, 0x42, 0x42, 0x42, 0x3C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x18, 0x24, 0x42, 0x42, 0x42, 0x26, 0x1A, 0x02, 0x02, 0x24, 0x38, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0x20, 
	0x00, 0x00, 0x00, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x20, 0x10, 0x08, 0x04, 0x02, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x40, 0x20, 0x10, 0x08, 0x04, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x62, 0x02, 0x04, 0x08, 0x08, 0x00, 0x18, 0x18, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x38, 0x44, 0x5A, 0xAA, 0xAA, 0xAA, 0xAA, 0xB4, 0x42, 0x44, 0x38, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x10, 0x10, 0x18, 0x28, 0x28, 0x24, 0x3C, 0x44, 0x42, 0x42, 0xE7, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xF8, 0x44, 0x44, 0x44, 0x78, 0x44, 0x42, 0x42, 0x42, 0x44, 0xF8, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3E, 0x42, 0x42, 0x80, 0x80, 0x80, 0x80, 0x80, 0x42, 0x44, 0x38, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xF8, 0x44, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x44, 0xF8, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xFC, 0x42, 0x48, 0x48, 0x78, 0x48, 0x48, 0x40, 0x42, 0x42, 0xFC, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xFC, 0x42, 0x48, 0x48, 0x78, 0x48, 0x48, 0x40, 0x40, 0x40, 0xE0, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3C, 0x44, 0x44, 0x80, 0x80, 0x80, 0x8E, 0x84, 0x44, 0x44, 0x38, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xE7, 0x42, 0x42, 0x42, 0x42, 0x7E, 0x42, 0x42, 0x42, 0x42, 0xE7, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x7C, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x7C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3E, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x88, 0xF0, 
	0x00, 0x00, 0x00, 0xEE, 0x44, 0x48, 0x50, 0x70, 0x50, 0x48, 0x48, 0x44, 0x44, 0xEE, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xE0, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x42, 0xFE, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xEE, 0x6C, 0x6C, 0x6C, 0x6C, 0x54, 0x54, 0x54, 0x54, 0x54, 0xD6, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xC7, 0x62, 0x62, 0x52, 0x52, 0x4A, 0x4A, 0x4A, 0x46, 0x46, 0xE2, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x38, 0x44, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x44, 0x38, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xFC, 0x42, 0x42, 0x42, 0x42, 0x7C, 0x40, 0x40, 0x40, 0x40, 0xE0, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x38, 0x44, 0x82, 0x82, 0x82, 0x82, 0x82, 0xB2, 0xCA, 0x4C, 0x38, 0x06, 0x00, 
	0x00, 0x00, 0x00, 0xFC, 0x42, 0x42, 0x42, 0x7C, 0x48, 0x48, 0x44, 0x44, 0x42, 0xE3, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3E, 0x42, 0x42, 0x40, 0x20, 0x18, 0x04, 0x02, 0x42, 0x42, 0x7C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xFE, 0x92, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x38, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xE7, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x3C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xE7, 0x42, 0x42, 0x44, 0x24, 0x24, 0x28, 0x28, 0x18, 0x10, 0x10, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xD6, 0x92, 0x92, 0x92, 0x92, 0xAA, 0xAA, 0x6C, 0x44, 0x44, 0x44, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xE7, 0x42, 0x24, 0x24, 0x18, 0x18, 0x18, 0x24, 0x24, 0x42, 0xE7, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xEE, 0x44, 0x44, 0x28, 0x28, 0x10, 0x10, 0x10, 0x10, 0x10, 0x38, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x7E, 0x84, 0x04, 0x08, 0x08, 0x10, 0x20, 0x20, 0x42, 0x42, 0xFC, 0x00, 0x00, 
	0x00, 0x1E, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x1E, 0x00, 
	0x00, 0x00, 0x40, 0x40, 0x20, 0x20, 0x10, 0x10, 0x10, 0x08, 0x08, 0x04, 0x04, 0x04, 0x02, 0x02, 
	0x00, 0x78, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x78, 0x00, 
	0x00, 0x1C, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 
	0x00, 0x60, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x42, 0x1E, 0x22, 0x42, 0x42, 0x3F, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xC0, 0x40, 0x40, 0x40, 0x58, 0x64, 0x42, 0x42, 0x42, 0x64, 0x58, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x22, 0x40, 0x40, 0x40, 0x22, 0x1C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x06, 0x02, 0x02, 0x02, 0x1E, 0x22, 0x42, 0x42, 0x42, 0x26, 0x1B, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x42, 0x7E, 0x40, 0x40, 0x42, 0x3C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x0F, 0x11, 0x10, 0x10, 0x7E, 0x10, 0x10, 0x10, 0x10, 0x10, 0x7C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3E, 0x44, 0x44, 0x38, 0x40, 0x3C, 0x42, 0x42, 0x3C, 
	0x00, 0x00, 0x00, 0xC0, 0x40, 0x40, 0x40, 0x5C, 0x62, 0x42, 0x42, 0x42, 0x42, 0xE7, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x30, 0x30, 0x00, 0x00, 0x70, 0x10, 0x10, 0x10, 0x10, 0x10, 0x7C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x0C, 0x0C, 0x00, 0x00, 0x1C, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x44, 0x78, 
	0x00, 0x00, 0x00, 0xC0, 0x40, 0x40, 0x40, 0x4E, 0x48, 0x50, 0x68, 0x48, 0x44, 0xEE, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x70, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x7C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEE, 0x92, 0x92, 0x92, 0x92, 0x92, 0xFE, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xDC, 0x62, 0x42, 0x42, 0x42, 0x42, 0xE7, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x42, 0x42, 0x42, 0x3C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD8, 0x64, 0x42, 0x42, 0x42, 0x44, 0x78, 0x40, 0xE0, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1E, 0x22, 0x42, 0x42, 0x42, 0x22, 0x1E, 0x02, 0x07, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEE, 0x32, 0x20, 0x20, 0x20, 0x20, 0xF8, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3E, 0x42, 0x40, 0x3C, 0x02, 0x42, 0x7C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0x7C, 0x10, 0x10, 0x10, 0x10, 0x10, 0x0C, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC6, 0x42, 0x42, 0x42, 0x42, 0x46, 0x3B, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE7, 0x42, 0x24, 0x24, 0x28, 0x10, 0x10, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD7, 0x92, 0x92, 0xAA, 0xAA, 0x44, 0x44, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0x24, 0x18, 0x18, 0x18, 0x24, 0x76, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE7, 0x42, 0x24, 0x24, 0x28, 0x18, 0x10, 0x10, 0xE0, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x44, 0x08, 0x10, 0x10, 0x22, 0x7E, 0x00, 0x00, 
	0x00, 0x03, 0x04, 0x04, 0x04, 0x04, 0x04, 0x08, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x03, 0x00, 
	0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 
	0x00, 0x60, 0x10, 0x10, 0x10, 0x10, 0x10, 0x08, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x60, 0x00, 
	0x30, 0x4C, 0x43, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

const uint16_t ball_icon[]={
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0060, 0x0900, 0x1DA2, 0x3227, 0x3A8A, 0x42AC, 0x42AC, 0x3A8A, 0x3227, 0x1DA2, 0x0900, 0x0060, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0020, 0x04C0, 0x25E4, 0x4EEF, 0x6BB7, 0x7BFD,
    0x7FFF, 0x7FFF, 0x7FFF, 0x7FFF, 0x7FFF, 0x7FFF, 0x7BFD, 0x6BB7, 0x4EEF, 0x25E4, 0x04C0, 0x0020, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x00C0, 0x2A25, 0x5F74, 0x7FFF, 0x7FFF, 0x7FFF, 0x7FFF, 0x7FFF, 0x7BFE, 0x7BFE, 0x7BFE, 0x7BFE, 0x7FFF,
    0x7FFF, 0x7FFF, 0x7FFF, 0x7FFF, 0x5F74, 0x2A25, 0x00C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0040, 0x1180, 0x4F0F, 0x7FFE, 0x7FFF,
    0x7BDE, 0x77BC, 0x77BC, 0x77BC, 0x77BC, 0x77BC, 0x77BC, 0x77BC, 0x77BC, 0x77BC, 0x77BC, 0x77BC, 0x77BC, 0x7BDE, 0x7FFF, 0x7FFE,
    0x4F0F, 0x1180, 0x0040, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0080, 0x1DE2, 0x5F74, 0x7FFF, 0x77BC, 0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 
    0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 0x6F9A, 0x77BC, 0x7FFF, 0x5F74, 0x1DE2, 0x0080, 0x0000, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x04A0, 0x2222, 0x6375,
    0x77BC, 0x6B79, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B78,
    0x6B78, 0x6B78, 0x6B78, 0x6B78, 0x6B79, 0x77BC, 0x6375, 0x2222, 0x04A0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x04A0, 0x1A00, 0x5B32, 0x6B99, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 
    0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 0x6356, 
    0x6B99, 0x5B32, 0x1A00, 0x04A0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0460,
    0x15C0, 0x46CC, 0x6777, 0x5B33, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34,
    0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B34, 0x5B33, 0x6777, 0x46CC, 0x15C0, 0x0460, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x1180, 0x2E64, 0x5F35, 0x5312, 0x5712, 0x5712, 0x5712,
    0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 
    0x5712, 0x5712, 0x5712, 0x5712, 0x5712, 0x5312, 0x5F35, 0x2E64, 0x1180, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0CE0, 0x1A20, 0x46AD, 0x5712, 0x4EEF, 0x4EF0, 0x4EF0, 0x4EF0, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10,
    0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4F10, 0x4EF0, 0x4EF0, 0x4EF0, 0x4EEF,
    0x5712, 0x46AD, 0x1A20, 0x0CE0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0020, 0x15E0, 0x2623, 0x4EF0, 0x4ACE, 
    0x4ACE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE,
    0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4AEE, 0x4ACE, 0x4ACE, 0x4EF0, 0x2623, 0x15E0, 0x0020, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x1100, 0x1A20, 0x3267, 0x4EEF, 0x46CD, 0x46CD, 0x46CD, 0x46CD, 0x46CD, 0x46CD, 0x46CD,
    0x46CD, 0x46CD, 0x46CD, 0x46CD, 0x46ED, 0x46ED, 0x46ED, 0x46ED, 0x46ED, 0x46ED, 0x46CD, 0x46CD, 0x46CD, 0x46CD, 0x46CD, 0x46CD,
    0x46CD, 0x46CD, 0x46CD, 0x46CD, 0x46CD, 0x4EEF, 0x3247, 0x1A20, 0x1100, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x19C0,
    0x1A00, 0x3A89, 0x46CD, 0x42AB, 0x42AB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB,
    0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42CB, 0x42AB, 0x42AB, 0x46CD,
    0x3A89, 0x1A00, 0x19C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0880, 0x1E40, 0x1E00, 0x3A89, 0x3EAB, 0x3AAA, 0x3EAA, 0x3EAA,
    0x3EAA, 0x3EAA, 0x3EAA, 0x3EAA, 0x3ECA, 0x3ECA, 0x3ECA, 0x3ECA, 0x3ECA, 0x3ECA, 0x3ECA, 0x3ECA, 0x3ECA, 0x3ECA, 0x3ECA, 0x3ECA,
    0x3ECA, 0x3ECA, 0x3EAA, 0x3EAA, 0x3EAA, 0x3EAA, 0x3EAA, 0x3EAA, 0x3AAA, 0x3EAB, 0x3A89, 0x1E00, 0x1E40, 0x0880, 0x0000, 0x0000,
    0x0000, 0x0000, 0x1120, 0x1E40, 0x1E01, 0x3689, 0x3AA9, 0x3A89, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 
    0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 0x3AA9, 
    0x3AA9, 0x3AA9, 0x3A89, 0x3AA9, 0x3689, 0x1E01, 0x1E40, 0x1120, 0x0000, 0x0000, 0x0000, 0x0000, 0x19A0, 0x1E20, 0x1E00, 0x3688,
    0x3688, 0x3688, 0x3688, 0x3688, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8,
    0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x36A8, 0x3688, 0x3688, 0x3688, 0x3688, 0x3688, 0x1E00, 
    0x1E20, 0x19A0, 0x0000, 0x0000, 0x0000, 0x0020, 0x1E00, 0x1E20, 0x1E00, 0x3266, 0x3287, 0x3286, 0x3286, 0x3286, 0x3286, 0x3286,
    0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6, 0x32A6,
    0x32A6, 0x32A6, 0x3286, 0x3286, 0x3286, 0x3286, 0x3286, 0x3287, 0x3266, 0x1E00, 0x1E20, 0x1E00, 0x0020, 0x0000, 0x0000, 0x0460, 
    0x1E20, 0x1E20, 0x1A00, 0x2A64, 0x3286, 0x2E85, 0x2E85, 0x2E85, 0x2E85, 0x2E85, 0x2E85, 0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 
    0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 0x2EA5, 0x2E85, 0x2E85, 0x2E85, 0x2E85, 0x2E85,
    0x2E85, 0x3286, 0x2A64, 0x1A00, 0x1E20, 0x1E20, 0x0460, 0x0000, 0x0000, 0x08A0, 0x2240, 0x1E20, 0x1A20, 0x2642, 0x2E85, 0x2A64,
    0x2A84, 0x2A84, 0x2A84, 0x2E84, 0x2E84, 0x2E84, 0x2EA4, 0x2EA4, 0x2EA4, 0x2EA4, 0x2EA4, 0x2EA4, 0x2EA4, 0x2EA4, 0x2EA4, 0x2EA4,
    0x2EA4, 0x2EA4, 0x2EA4, 0x2EA4, 0x2E84, 0x2E84, 0x2E84, 0x2A84, 0x2A84, 0x2A84, 0x2A64, 0x2E85, 0x2642, 0x1A20, 0x1E20, 0x2240, 
    0x08A0, 0x0000, 0x0000, 0x08A0, 0x2260, 0x1E20, 0x1E20, 0x1E40, 0x2A64, 0x2A64, 0x2A64, 0x2A84, 0x2A84, 0x2A84, 0x2A84, 0x2A84, 
    0x2AA4, 0x2AA4, 0x2AA3, 0x2AA3, 0x2AA3, 0x2AA3, 0x2AA3, 0x2AA3, 0x2AA3, 0x2AA3, 0x2AA3, 0x2AA3, 0x2AA4, 0x2AA4, 0x2A84, 0x2A84, 
    0x2A84, 0x2A84, 0x2A84, 0x2A64, 0x2A64, 0x2A64, 0x1E40, 0x1E20, 0x1E20, 0x2260, 0x08A0, 0x0000, 0x0000, 0x08A0, 0x2260, 0x1E20, 
    0x1E40, 0x1E40, 0x2261, 0x2A83, 0x2663, 0x2683, 0x2683, 0x2683, 0x2683, 0x2683, 0x26A3, 0x26A3, 0x26A3, 0x26A3, 0x26A3, 0x26A3, 
    0x26A3, 0x26A3, 0x26A3, 0x26A3, 0x26A3, 0x26A3, 0x26A3, 0x26A3, 0x2683, 0x2683, 0x2683, 0x2683, 0x2683, 0x2663, 0x2A83, 0x2261,
    0x1E40, 0x1E40, 0x1E20, 0x2260, 0x08A0, 0x0000, 0x0000, 0x08A0, 0x2260, 0x1E40, 0x1E40, 0x1E40, 0x1E40, 0x2261, 0x2682, 0x2682,
    0x2682, 0x2682, 0x2682, 0x2682, 0x26A2, 0x26A2, 0x26A3, 0x26A3, 0x22A3, 0x22A3, 0x22A3, 0x22A3, 0x22A3, 0x22A3, 0x26A3, 0x26A3,
    0x26A2, 0x26A2, 0x2682, 0x2682, 0x2682, 0x2682, 0x2682, 0x2682, 0x2261, 0x1E40, 0x1E40, 0x1E40, 0x1E40, 0x2260, 0x08A0, 0x0000,
    0x0000, 0x0480, 0x2260, 0x1E40, 0x1E40, 0x1E40, 0x1E40, 0x1E60, 0x2261, 0x2281, 0x2281, 0x2281, 0x2281, 0x22A2, 0x22A2, 0x22A3, 
    0x22A3, 0x22A4, 0x22C4, 0x1EC4, 0x1EC4, 0x1EC4, 0x1EC4, 0x22C4, 0x22A4, 0x22A3, 0x22A3, 0x22A2, 0x22A2, 0x2281, 0x2281, 0x2281,
    0x2281, 0x2261, 0x1E60, 0x1E40, 0x1E40, 0x1E40, 0x1E40, 0x2260, 0x0480, 0x0000, 0x0000, 0x0020, 0x1E40, 0x1E60, 0x1E40, 0x1E40,
    0x1E60, 0x1E60, 0x1E60, 0x2280, 0x2280, 0x2281, 0x22A2, 0x1EA3, 0x1EA3, 0x1EA4, 0x1EC4, 0x1EC4, 0x1EC5, 0x1EC5, 0x1EC5, 0x1EC5,
    0x1EC5, 0x1EC5, 0x1EC4, 0x1EC4, 0x1EA4, 0x1EA3, 0x1EA3, 0x22A2, 0x2281, 0x2280, 0x2280, 0x1E60, 0x1E60, 0x1E60, 0x1E40, 0x1E40, 
    0x1E60, 0x1E40, 0x0020, 0x0000, 0x0000, 0x0000, 0x19E0, 0x2280, 0x1E40, 0x1E60, 0x1E60, 0x1E60, 0x1E80, 0x1E80, 0x1E81, 0x1EA2, 
    0x1EA3, 0x1EA3, 0x1AC4, 0x1AC5, 0x1AC5, 0x1AC5, 0x1AC6, 0x1AC6, 0x1AC6, 0x1AC6, 0x1AC6, 0x1AC6, 0x1AC5, 0x1AC5, 0x1AC5, 0x1AC4, 
    0x1EA4, 0x1EA3, 0x1EA2, 0x1E81, 0x1E80, 0x1E80, 0x1E60, 0x1E60, 0x1E60, 0x1E40, 0x2280, 0x19E0, 0x0000, 0x0000, 0x0000, 0x0000, 
    0x1160, 0x22C0, 0x1E60, 0x1E60, 0x1E60, 0x1E80, 0x1E80, 0x1E81, 0x1EA2, 0x1EA3, 0x1AA4, 0x1AC4, 0x1AC5, 0x1AC5, 0x1AC6, 0x1AE6, 
    0x1AE7, 0x1AE7, 0x16E7, 0x16E7, 0x1AE7, 0x16E7, 0x1AE6, 0x1AC6, 0x1AC5, 0x1AC5, 0x1AC4, 0x1AA4, 0x1EA3, 0x1EA2, 0x1E81, 0x1E80,
    0x1E80, 0x1E60, 0x1E60, 0x1E60, 0x22C0, 0x1160, 0x0000, 0x0000, 0x0000, 0x0000, 0x08A0, 0x22C0, 0x1E60, 0x1E60, 0x1E80, 0x1E80, 
    0x1E81, 0x1EA2, 0x1EA3, 0x1AC4, 0x1AC5, 0x1AC5, 0x1AE6, 0x1AE7, 0x16E7, 0x16E8, 0x16E8, 0x16E8, 0x16E8, 0x16E8, 0x16E8, 0x16E8,
    0x16E8, 0x16E7, 0x1AE7, 0x1AE6, 0x1AC5, 0x1AC5, 0x1AC4, 0x1EA3, 0x1EA2, 0x1E81, 0x1E80, 0x1E80, 0x1E60, 0x1E60, 0x22C0, 0x08A0,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x1E40, 0x22A0, 0x1E60, 0x1E80, 0x1E81, 0x1EA2, 0x1EA3, 0x1AC4, 0x1AC5, 0x1AC6, 0x1AE7,
    0x16E7, 0x16E8, 0x1709, 0x1709, 0x1709, 0x1709, 0x170A, 0x170A, 0x1709, 0x1709, 0x1709, 0x1708, 0x16E8, 0x16E7, 0x1AE7, 0x1AC6,
    0x1AC5, 0x1AC4, 0x1EA3, 0x1EA2, 0x1E81, 0x1E80, 0x1E60, 0x22A0, 0x1E40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x1140, 
    0x26E0, 0x1E60, 0x1E81, 0x1EA2, 0x1EA3, 0x1EC4, 0x1AC5, 0x1AE6, 0x1AE7, 0x1AE8, 0x1708, 0x1709, 0x170A, 0x172A, 0x172A, 0x172B, 
    0x172B, 0x172B, 0x172B, 0x172A, 0x170A, 0x170A, 0x1709, 0x1708, 0x1AE8, 0x1AE7, 0x1AE6, 0x1AC5, 0x1EC4, 0x1EA3, 0x1EA2, 0x1E81, 
    0x1E60, 0x26E0, 0x1140, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0020, 0x22A0, 0x22A0, 0x1EA2, 0x1EA3, 0x1EC4, 0x1EC5,
    0x1AE6, 0x1AE7, 0x1B08, 0x1B09, 0x1709, 0x172A, 0x172B, 0x172B, 0x172C, 0x172C, 0x172C, 0x172C, 0x172C, 0x172C, 0x172B, 0x172B, 
    0x172A, 0x1709, 0x1B09, 0x1B08, 0x1AE7, 0x1AE6, 0x1EC5, 0x1EC4, 0x1EA3, 0x1EA2, 0x22A0, 0x22A0, 0x0020, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x1140, 0x2721, 0x1EA2, 0x1AA4, 0x1EC5, 0x1EE6, 0x1EE7, 0x1B08, 0x1B09, 0x1B0A, 0x172B, 0x172B,
    0x172C, 0x174D, 0x174D, 0x174D, 0x174D, 0x174D, 0x174D, 0x174D, 0x174D, 0x172C, 0x172B, 0x172B, 0x1B2A, 0x1B09, 0x1B08, 0x1EE7,
    0x1EE6, 0x1EC5, 0x1AA4, 0x1AA2, 0x2721, 0x1140, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x1A62,
    0x1F03, 0x1AA4, 0x1AC5, 0x22E6, 0x1F08, 0x1F09, 0x1B2A, 0x1B2B, 0x1B2C, 0x1B4C, 0x174D, 0x174E, 0x176E, 0x176E, 0x176F, 0x176F,
    0x176E, 0x176E, 0x174E, 0x174D, 0x1B4C, 0x1B2C, 0x1B2B, 0x1B2A, 0x1F09, 0x1F08, 0x22E7, 0x1AC5, 0x1AA4, 0x1F03, 0x1A62, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0481, 0x1F24, 0x1B05, 0x16C6, 0x2307, 0x2308, 0x1F29, 
    0x1F2B, 0x1F4C, 0x1B4D, 0x1B4D, 0x1B6E, 0x1B6F, 0x1B6F, 0x1770, 0x1770, 0x1770, 0x1770, 0x1B6F, 0x1B6F, 0x1B6E, 0x1B4D, 0x1B4D,
    0x1F4C, 0x1F2B, 0x1F2A, 0x2309, 0x2307, 0x16C6, 0x1B05, 0x1F24, 0x0480, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0902, 0x1F66, 0x1707, 0x16E8, 0x2729, 0x272A, 0x234B, 0x1F4C, 0x1F6D, 0x1F6E, 0x1F6F, 0x1F70, 
    0x1B91, 0x1B91, 0x1B91, 0x1B91, 0x1B91, 0x1B91, 0x1F70, 0x1F6F, 0x1F6E, 0x1F6D, 0x1F4D, 0x234B, 0x272A, 0x2729, 0x16E8, 0x1707,
    0x1F66, 0x0902, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0D63,
    0x1B89, 0x1329, 0x1B0A, 0x2B4B, 0x2B4C, 0x236D, 0x236E, 0x236F, 0x2390, 0x2391, 0x1F92, 0x1F92, 0x1FB2, 0x1FB2, 0x1FB2, 0x1F92,
    0x2391, 0x2390, 0x236F, 0x236E, 0x236D, 0x2B4C, 0x2B4B, 0x1B0A, 0x1329, 0x1B89, 0x0D63, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0943, 0x178A, 0x136C, 0x172B, 0x2B6D, 0x336E,
    0x2F6F, 0x2790, 0x2791, 0x27B2, 0x27B3, 0x27B3, 0x27B4, 0x27B4, 0x27B3, 0x27B3, 0x27B2, 0x2791, 0x2790, 0x2B8F, 0x336E, 0x2B6D,
    0x172B, 0x136C, 0x178A, 0x0943, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x04E3, 0x130A, 0x0FCE, 0x0F6E, 0x236E, 0x3390, 0x3BB1, 0x37B2, 0x33B3, 0x2FB4, 0x2FD5, 
    0x2FD5, 0x2FD5, 0x2FD5, 0x2FB4, 0x33B3, 0x37B2, 0x3BB1, 0x3390, 0x236E, 0x0F6E, 0x0FCE, 0x130A, 0x04E3, 0x0000, 0x0000, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
    0x0021, 0x0E28, 0x0F8F, 0x0BF2, 0x13B1, 0x2391, 0x2FB3, 0x3BD4, 0x3FD5, 0x43D6, 0x43D6, 0x43D6, 0x43D6, 0x3FD5, 0x3BD4, 0x2FB3,
    0x2391, 0x13B1, 0x0BF2, 0x0F8F, 0x0E28, 0x0021, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x04C3, 0x0A4A, 0x0B91, 0x03F5,
    0x07F6, 0x0FF6, 0x17F7, 0x1BF7, 0x1FF8, 0x1FF8, 0x1BF7, 0x17F7, 0x0FF6, 0x07F6, 0x03F5, 0x0B91, 0x0A4A, 0x04C3, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0062, 0x0588, 0x068D, 0x0352, 0x03B6, 0x03F9, 0x03FB, 0x03FB, 
    0x03F9, 0x03B6, 0x0352, 0x068D, 0x0588, 0x0062, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0083, 0x00E5, 0x0106, 0x0106, 0x00E5, 0x0083, 0x0000, 0x0000, 0x0000, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000
    };

uint16_t roun[14]={0X098C,0X0A0E,0X0A90,0X0AB2,0X090A,0X090A,0X090A,0X090A,0X090A,0X090A,0X090A,0X090A,0X090A,0X090A};

uint8_t  key_chr[38]={49,50,51,52,53,54,55,56,57,48,
                 81,87,69,82,84,89,85,73,79,80,
                 65,83,68,70,71,72,74,75,76,
                 0,90,88,67,86,66,78,77,0};

uint16_t keyx[38]={48,120,192,264,336,408,480,552,624,696,
              48,120,192,264,336,408,480,552,624,696,
              84,156,228,300,372,444,516,588,660,
              44,156,228,300,372,444,516,588,660};

uint16_t keyy[38]={272,272,272,272,272,272,272,272,272,272,
              324,324,324,324,324,324,324,324,324,324,
              376,376,376,376,376,376,376,376,376,
              428,428,428,428,428,428,428,428,428};
                 
/* Default LCD configuration with LCD Layer 1 */
uint32_t CurrentFrameBuffer = FOREG_BUF;  //��ǰ֡�����ַ
uint32_t CurrentLayer = LCD_FOREGROUND_LAYER;    //��ǰ��
/* Global variables to set the written text color */
static uint16_t CurrentTextColor ;  // = 0x0000;       //��ǰ�ı���ɫ
static uint16_t CurrentBackColor ;  // = 0xFFFF;
static uint8_t LCD_Currentfonts;



uint8_t  casebuf_rec[256]={0};       //��ʱ�����洢��--------------------------------------------------

extern uint8_t  cursor_seat[5];
extern uint8_t  boxdata[5][5];                 //���������
extern uint16_t current;
extern uint16_t buoy_seat,last_seat; 
extern uint8_t  fcase;
extern uint8_t  page;
extern uint8_t  colu;
extern uint8_t  inbox;
extern uint8_t  key_num;
extern uint8_t  fcas[];
extern uint16_t pole_sta,pole_dir;
extern uint8_t  ctrldata[];

extern uint8_t W25QXX_BUFFER[];
extern FIL fil;
extern UINT fnum;

static uint16_t color_trans(uint16_t color)
{
	color=((color&0xffc0)>>1) | (color&0x1f) ;      //ת����͸��ARGB��ʽ�����͸��ɫ
	return color;
}

static void change_backdrop(uint32_t bj)
{
	LTDC_Layer1->CFBAR=bj;
	LTDC->SRCR = (uint32_t)LTDC_SRCR_IMR;
}

void LCD_SetTextColor(__IO uint16_t Color)
{
	CurrentTextColor=Color;
}

void LCD_SetBackColor(__IO uint16_t Color)
{
  CurrentBackColor = Color;    //���ʱ���ɫ͸��
}

void drow_ball(uint16_t x, uint16_t y, uint8_t afa)
{
    uint16_t i,j,pts;
    uint32_t addres;
    pts=0;
    addres = FOREG_BUF+((x + y*800)<<1);
    for(i=0;i<44;i++)
    {
        for(j=0;j<42;j++)
        {
            if(afa)
            {
                if(ball_icon[pts]) *(uint16_t*)addres = ball_icon[pts]|0x8000;
            }
            else 
            {
                if(ball_icon[pts]) *(uint16_t*)addres = ball_icon[pts];
            }                
            pts++;
            addres+=2;
        }
        addres+=1516;
    }
}

void fill_rect(uint16_t x, uint16_t y, uint16_t width, uint16_t height)
{
    uint32_t timeout=0;
    uint32_t  Xaddress = 0,colorformat; 
    uint16_t tempc;
    
    if(width==0 || height==0) return;
    if(CurrentLayer == LCD_FOREGROUND_LAYER)
	{
		colorformat=CM_ARGB1555;
		tempc=color_trans(CurrentTextColor)|0x8000;
	}
	else
	{
		colorformat=CM_RGB565;
		tempc=CurrentTextColor;
	}
    
    Xaddress = CurrentFrameBuffer + 2*(800*y + x);
    
	__HAL_RCC_DMA2D_CLK_ENABLE(); //ʹ�� DM2D ʱ��
    DMA2D->CR&=~(DMA2D_CR_START); //��ֹͣ DMA2D
    DMA2D->CR=DMA2D_R2M; //�Ĵ������洢��ģʽ
    DMA2D->OPFCCR=colorformat; //������ɫ��ʽ
    DMA2D->OOR=800-width; //������ƫ��
    DMA2D->OMAR=Xaddress; //����洢����ַ
    DMA2D->NLR=(uint32_t)(width << 16) | height; //�趨�����Ĵ���
    DMA2D->OCOLR=tempc; //�趨�����ɫ�Ĵ���
    DMA2D->CR|=DMA2D_CR_START; //���� DMA2D
    while((DMA2D->ISR&(DMA2D_FLAG_TC))==0) //�ȴ��������
    {
        timeout++;
        if(timeout>0X1FFFFF)break;//��ʱ�˳�
    }
    DMA2D->IFCR|=DMA2D_FLAG_TC;//���������ɱ�־
}

void roun_plot(void)
{
	uint8_t i;
	for(i=0;i<10;i++)
	{
		LCD_SetTextColor(roun[i]);
		fill_rect(304+i*20,292,8,9);
	}
	for(i=13;i>0;i--)
	{
		roun[i]=roun[i-1];
	}
	roun[0]=roun[13];
}

void LCD_SetColors(uint16_t TextColor, uint16_t BackColor)
{
    CurrentTextColor = TextColor;  
    CurrentBackColor = BackColor;  
}

void set_layer(uint32_t page)
{
	if(page == FOREG_BUF) CurrentLayer = LCD_FOREGROUND_LAYER;
	else CurrentLayer = LCD_BACKGROUND_LAYER;
	CurrentFrameBuffer = page;
}

void screen_clear(uint16_t color)
{
    uint16_t tempc;
    uint32_t timeout=0;
	
	if (CurrentLayer == LCD_FOREGROUND_LAYER)
		tempc=color_trans(color);
	else tempc=color;
    
    __HAL_RCC_DMA2D_CLK_ENABLE(); //ʹ�� DM2D ʱ��
    DMA2D->CR&=~(DMA2D_CR_START); //��ֹͣ DMA2D
    DMA2D->CR=DMA2D_R2M; //�Ĵ������洢��ģʽ
    if(CurrentLayer == LCD_FOREGROUND_LAYER)  DMA2D->OPFCCR=DMA2D_INPUT_ARGB1555;
    else  DMA2D->OPFCCR=DMA2D_INPUT_RGB565; //������ɫ��ʽ
    DMA2D->OOR=0; //������ƫ��
    DMA2D->OMAR=CurrentFrameBuffer; //����洢����ַ
    DMA2D->NLR=480|(800<<16); //�趨�����Ĵ���
    DMA2D->OCOLR=tempc; //�趨�����ɫ�Ĵ���
    DMA2D->CR|=DMA2D_CR_START; //���� DMA2D
    while((DMA2D->ISR&(DMA2D_FLAG_TC))==0) //�ȴ��������
    {
        timeout++;
        if(timeout>0X1FFFFF)break;//��ʱ�˳�
    }
    DMA2D->IFCR|=DMA2D_FLAG_TC;//���������ɱ�־
}

void put_icon(uint16_t x, uint16_t y, uint8_t icon_number,uint16_t color)
{
	uint16_t width,high,i,j,m,n,move,num,tempc;
	uint8_t  pix,*p;
	p = W25QXX_BUFFER;
	
	m=x;n=y;
	if(CurrentLayer==LCD_BACKGROUND_LAYER) tempc=color;
	else tempc=0x8000|(color_trans(color));
	
	switch(icon_number)
	{
		case WEL:      width=264; high=40;  move=0;     num=1320;  break;
		case LOGIN:    width=24;  high=32;  move=1320;  num=96;    break;
		case DJ:       width=24;  high=32;  move=1416;  num=96;    break;
		case CJ:       width=32;  high=32;  move=1512;  num=128;   break;
		case CAS:      width=32;  high=32;  move=1640;  num=128;   break;
		case BACK:     width=32;  high=24;  move=1768;  num=96;    break;
		case FIND:     width=24;  high=24;  move=1864;  num=72;    break;
		case KEY:      width=56;  high=40;  move=1936;  num=280;   break;
		case SAVE:     width=24;  high=24;  move=2216;  num=72;    break;
		case ZDY:      width=24;  high=24;  move=2288;  num=72;    break;
		case LISTCA :  width=136; high=56;  move=2360;  num=952;   break;
		case ZDYBACK:  width=96;  high=40;  move=3312;  num=480;   break;
		case WORK:     width=208; high=128; move=3792;  num=3328;  break;
		case BUOY:     width=120; high=52;  move=7120;  num=780;   break;
		case RATE_L:   width=16 ; high=32;  move=7900;  num=64;    break;
		case RATE_R:   width=16 ; high=32;  move=7964;  num=64;    break;
		case PAD_DT:   width=52 ; high=56;  move=8028;  num=392;   break;
	}
	
    get_data_from_file("0:icon.bin",W25QXX_BUFFER,move,num);
	for(i=0;i<num;i++)
	{
		pix=*p;
		p++;
		for(j=0;j<8;j++)
		{
			if(pix&0x80)
				*(__IO uint16_t*) (CurrentFrameBuffer+2*(m+n*800)) = tempc;
			m++;
			if(m==(x+width))
			{
				m=x;n++;
				if(icon_number==PAD_DT) j=8;            //��������ִ����̣����ȡ�¸�����
			}
			if(n==(y+high)) return;
			pix<<=1;
		}
	}	
}

void show_logo(void)
{
    uint8_t *dst;
    uint16_t i;
    uint32_t offset=0;
    dst=(uint8_t*)(0xC0000000+128636);    //logo��������ʾ��ַ
    f_open(&fil,"0:logo.bin",FA_READ);
    for(i=0;i<172;i++)
    {
        f_lseek(&fil,offset);
        f_read(&fil,dst,328,&fnum);
        offset+=328; dst+=1600;
    }
    f_close(&fil);
//    put_icon(265,340,WEL,ICON_COLOR);      //���û�ӭҳ������
//    dst=(uint8_t*)(0xC0000000+544300);    //logo��������ʾ��ַ
//    f_open(&fil,"0:wenzi.bin",FA_READ);
//    for(i=0;i<47;i++)
//    {
//        f_lseek(&fil,offset);
//        f_read(&fil,dst,990,&fnum);
//        offset+=990; dst+=1600;
//    }
//    f_close(&fil);
}

void DMA2D_Copy(uint8_t name, uint32_t page)
{
    uint16_t offset,width,hight,xPos,yPos;
    uint32_t pSrc, pDst;
    uint32_t timeout=0;
    
    switch(name)
    {
        case LEFT_PIC :  xPos=30;  yPos=40;  width=344; hight=270; pSrc=LEFT_BUF-2;   break;
		case RIGHT_PIC :  xPos=426; yPos=40;  width=344; hight=270; pSrc=RIGHT_BUF-2;  break;
		case WAR_PIC  :  xPos=125; yPos=132; width=157; hight=168; pSrc=WAR_BUF;    break;
		case BOTT_PIC :  xPos=0;   yPos=420; width=800; hight=60;  pSrc=DIBIAN_BUF; break;
        case PLOGO_PIC:  xPos=10;   yPos=11; width=66; hight=66;  pSrc=PLOGO_BUF-2; break;
        case WENZI_PIC:  xPos=155;   yPos=340; width=495; hight=47;  pSrc=WENZI_BUF-2; break;
    }
    offset = 800-width;
    pDst=(page+2*(xPos+yPos*800));
    
    __HAL_RCC_DMA2D_CLK_ENABLE(); //ʹ�� DM2D ʱ��
    DMA2D->CR&=~(DMA2D_CR_START); //��ֹͣ DMA2D
    
    if(page==FOREG_BUF)
	{
		DMA2D->CR = DMA2D_M2M_PFC;
		DMA2D->OPFCCR=DMA2D_OUTPUT_ARGB1555;
	} 
    else 	
	{
		DMA2D->CR = DMA2D_M2M;
		DMA2D->OPFCCR=DMA2D_OUTPUT_RGB565;
	}

    DMA2D->FGMAR = pSrc;
    DMA2D->FGPFCCR = DMA2D_INPUT_RGB565;
    DMA2D->FGOR = 0;
    
    DMA2D->OMAR = pDst; //����洢����ַ
    DMA2D->OOR = offset; //������ƫ��
    DMA2D->NLR = (uint32_t)(width << 16) | hight; //�趨�����Ĵ���

    DMA2D->CR|=DMA2D_CR_START; //���� DMA2D
    while((DMA2D->ISR&(DMA2D_FLAG_TC))==0) //�ȴ��������
    {
        timeout++;
        if(timeout>0X1FFFFF)break;//��ʱ�˳�
    }
    DMA2D->IFCR|=DMA2D_FLAG_TC;//���������ɱ�־
    
}

void LCD_DrawLine(uint16_t Xpos, uint16_t Ypos, uint16_t Length, uint8_t Direction)
{
    uint32_t  Xaddress = 0;
    uint16_t tempc;
    
    Xaddress = CurrentFrameBuffer + 2*(800*Ypos + Xpos);
    if(CurrentTextColor==0) tempc=0;
    else if(CurrentLayer==LCD_FOREGROUND_LAYER)
		tempc=color_trans(CurrentTextColor)|0x8000;
	else
		tempc=CurrentTextColor;
    
    if(Direction == LCD_DIR_HORIZONTAL)
    {
        for(;Length>0;Length--)
        {
            *(__IO uint16_t*) Xaddress = tempc; Xaddress+=2;
        }
    }
    else
    {
        for(;Length>0;Length--)
        {
            *(__IO uint16_t*) Xaddress = tempc; Xaddress+=1600;
        }
    }
}

void LCD_DrawCircle(uint16_t Xpos, uint16_t Ypos, uint16_t Radius)
{
	uint16_t tempc;
    int x = -Radius, y = 0, err = 2-2*Radius, e2;
	
	if(CurrentTextColor==0) tempc=0;
    else if (CurrentLayer == LCD_FOREGROUND_LAYER)
		tempc=color_trans(CurrentTextColor)|0x8000;
	else tempc=CurrentTextColor;
	
    do {
        *(__IO uint16_t*) (CurrentFrameBuffer + (2*((Xpos-x) + 800*(Ypos+y)))) = tempc; 
        *(__IO uint16_t*) (CurrentFrameBuffer + (2*((Xpos+x) + 800*(Ypos+y)))) = tempc;
        *(__IO uint16_t*) (CurrentFrameBuffer + (2*((Xpos+x) + 800*(Ypos-y)))) = tempc;
        *(__IO uint16_t*) (CurrentFrameBuffer + (2*((Xpos-x) + 800*(Ypos-y)))) = tempc;
      
        e2 = err;
        if (e2 <= y) {
            err += ++y*2+1;
            if (-x == y && e2 <= x) e2 = 0;
        }
        if (e2 > x) err += ++x*2+1;
    }
    while (x <= 0);
}

void LCD_DrawFullCircle(uint16_t Xpos, uint16_t Ypos, uint16_t Radius)
{
  int32_t  D;    /* Decision Variable */ 
  uint32_t  CurX;/* Current X Value */
  uint32_t  CurY;/* Current Y Value */ 
  
  D = 3 - (Radius << 1);
  
  CurX = 0;
  CurY = Radius;
  
  while (CurX <= CurY)
  {
    if(CurY > 0) 
    {
      LCD_DrawLine(Xpos - CurX, Ypos - CurY, 2*CurY, LCD_DIR_VERTICAL);
      LCD_DrawLine(Xpos + CurX, Ypos - CurY, 2*CurY, LCD_DIR_VERTICAL);
    }
    
    if(CurX > 0) 
    {
      LCD_DrawLine(Xpos - CurY, Ypos - CurX, 2*CurX, LCD_DIR_VERTICAL);
      LCD_DrawLine(Xpos + CurY, Ypos - CurX, 2*CurX, LCD_DIR_VERTICAL);
    }
    if (D < 0)
    { 
      D += (CurX << 2) + 6;
    }
    else
    {
      D += ((CurX - CurY) << 2) + 10;
      CurY--;
    }
    CurX++;
  }
  
  LCD_DrawCircle(Xpos, Ypos, Radius);  
}

void LCD_SetFont(uint8_t font)
{
	LCD_Currentfonts = font;
}

void	Get_GBK_DZK(uint8_t *code, uint8_t *hzdata, uint16_t size)
{
 	uint8_t GBKH,GBKL;               // GBK���λ���λ					  
 	uint32_t offset; 	         // ����ƫ����
	
 	GBKH=*code;
 	GBKL=*(code+1);	     // GBKL=*(code+1);
 	if(GBKH>0XFE||GBKH<0X81)return;
 	GBKH-=0x81;
	if(GBKL<0X7F) GBKL-=0X40;
	else 	GBKL-=0x41;
 	offset=(uint32_t)((190*GBKH+GBKL)*size); //�õ��ֿ��е��ֽ�ƫ���� 
	
	switch(LCD_Currentfonts)
	{
		case 16: get_data_from_file("0:gbk16s.bin",hzdata,offset,size); break;
		case 24: get_data_from_file("0:gbk24s.bin",hzdata,offset,size); break;
	}	  
 	return;
}

void	Get_ASCII(uint8_t *code, uint8_t *ascdata, uint16_t size)
	{
		uint32_t ascii_offset;
		uint8_t temp,i;

		temp=*code;
		ascii_offset=(uint32_t)((temp-0x20)*size);
		
		switch(LCD_Currentfonts)
		{
			case 16: for(i=0;i<size;i++)
                    {
                        *ascdata=*(FON_asc16+i+ascii_offset);
						ascdata++; 
                    } break;
			case 24: get_data_from_file("0:asc24s.bin",ascdata,ascii_offset,size);	 break;
		}
	}

static uint8_t dz_data[128];
    
void LCD_DrawChar(uint16_t Xpos, uint16_t Ypos, uint8_t t, uint8_t *c)
{
  uint32_t index = 0, counter = 0, xpos =0;
  uint32_t  Xaddress = 0;
  uint16_t size,tempc,temptc;
  uint8_t temp_chr=0, cnt_chr=0;
  uint8_t char_width, char_height;  // 添加字符宽度和高度变量

	switch(LCD_Currentfonts)                             //�õ�ASCII�ַ���Ӧ������ռ���ֽ���
	{
		case 16: size=16; char_width=8; char_height=16; break;   // 16号字体：8x16像素
		case 24: size=48; char_width=12; char_height=24; break;  // 24号字体：12x24像素
		case 32: size=64; char_width=16; char_height=32; break;  // 32号字体：16x32像素
		default: size=16; char_width=8; char_height=16; break;   // 默认16号字体
	}

	Get_ASCII(c,dz_data,size);

    xpos = Xpos*800*2;
    Xaddress += Ypos;

	if(CurrentLayer == LCD_FOREGROUND_LAYER)
	{
		temptc = color_trans(CurrentTextColor)|0x8000;  //��͸��
        tempc  = color_trans(CurrentBackColor);
		if(t) tempc|=0x8000;
	}
	else
	{
		temptc = CurrentTextColor;
		tempc  = CurrentBackColor;
	}

	for(index = 0; index < size; index++)
  {
    temp_chr=dz_data[index];
	for(counter = 0; counter < 8; counter++)
    {
      cnt_chr++;                   //计数器，用来控制字符宽度
      if(temp_chr&0x80)
      {
          /* Write data value to all SDRAM memory */
         *(__IO uint16_t*) (CurrentFrameBuffer + (2*Xaddress) + xpos) = temptc;    //设置前景色不透明
      }
      else
      {
          /* Write data value to all SDRAM memory */
         *(__IO uint16_t*) (CurrentFrameBuffer + (2*Xaddress) + xpos) = tempc;
      }
      if(cnt_chr==char_width)                                      //到达字符宽度换行
      {
          Xaddress += ((800 - char_width)+1);            //换行
          cnt_chr=0;                                                           //字符宽度计数器清零
          counter=8;                                                           //跳出内循环
      }
      else                                                                   //否则，继续下一个点的操作
      {
          temp_chr<<=1;
          Xaddress++;
      }
    }
  }
}

/**
  * @brief  ��LCD����ϻ�����.
  * @param  Xpos: �ַ����е�ַ.
  * @param  Ypos: �ַ���ʼ��ַ.
  * @param  c: �ַ�����ָ��.
  * @param  size: ���ַ������С.
  * @retval None
  */
void LCD_DrawHz(uint16_t Xpos, uint16_t Ypos, uint8_t t, uint8_t *c)
{
  uint32_t index = 0, counter = 0, xpos =0;
  uint32_t  Xaddress = 0;
  uint8_t csize=0,cnt=0,temp;                             //�ַ���Ӧ������ռ���ֽ���
  uint16_t tempc,temptc;
	
	switch(LCD_Currentfonts)                             //�õ�����һ���ַ���Ӧ������ռ���ֽ���
	{
		case 16: csize=32; break;
		case 24: csize=72; break;
		case 32: csize=128; break;
	}
	
	Get_GBK_DZK(c, dz_data, csize);
	
	xpos = Xpos*800*2;          //�ַ���������ʼ��ַ
	Xaddress += Ypos;                       //�ַ����󻭵��ַ
	
	if(CurrentLayer == LCD_FOREGROUND_LAYER)
	{
		temptc = color_trans(CurrentTextColor)|0x8000;  //��͸��
	    tempc  = color_trans(CurrentBackColor);
		if(t) tempc|=0x8000;
	}
	else
	{
		temptc = CurrentTextColor;
		tempc  = CurrentBackColor;
	}
	
	 for(index = 0; index < csize; index++)
    {
        temp=dz_data[index];
        for(counter = 0; counter < 8; counter++)
        {
          if(temp&0x80)
          {
              /* Write data value to all SDRAM memory */
             *(__IO uint16_t*) (CurrentFrameBuffer + (2*Xaddress) + xpos) = temptc;
          }
          else 
          {
              /* Write data value to all SDRAM memory */
             *(__IO uint16_t*) (CurrentFrameBuffer + (2*Xaddress) + xpos) = tempc;         
          }
          temp<<=1;
          Xaddress++;
          cnt++;
          if(cnt==LCD_Currentfonts)
          {
              Xaddress += (800 - LCD_Currentfonts);                //����
              cnt=0;
          }			
        }
    }
}

void LCD_DisplayStringLine(uint16_t Line, uint16_t Column, uint8_t t, uint8_t *ptr)
{
  uint16_t refcolumn = Column;
  uint8_t char_width;  // 添加字符宽度变量

  // 根据字体大小确定字符宽度
  switch(LCD_Currentfonts)
  {
    case 16: char_width=8; break;   // 16号字体宽度为8像素
    case 24: char_width=12; break;  // 24号字体宽度为12像素
    case 32: char_width=16; break;  // 32号字体宽度为16像素
    default: char_width=8; break;   // 默认宽度
  }

	while(*ptr !=0)                                       //����ַ�û�н���
	{
		if(*ptr>0x80)                                       //����������ַ�
		{
			if(refcolumn>(800-LCD_Currentfonts-2))          //����������������У��߾�Ԥ��2
			{
				Line+=LCD_Currentfonts;
				refcolumn=2;                                    //���к��л���,�����߾�
			}
			LCD_DrawHz(Line, refcolumn,t,ptr);                 //��ʾһ������
			refcolumn+=LCD_Currentfonts;                      //����һ�������ַ�����
			ptr+=2;                                           //ָ���¸������ַ�
		}
		else
		{
			if(refcolumn > (800 - char_width-2))
			{
				Line+=LCD_Currentfonts;                            //����
				refcolumn=2;
			}
			/* Display one character on LCD */
			LCD_DrawChar(Line, refcolumn,t, ptr);
			/* Decrement the column position by width */
			refcolumn += char_width;
			/* Point on the next character */
			ptr++;

		}
	}
}

void Lcd_clear_rect(uint16_t Xpos,uint16_t Ypos,uint16_t Width,uint16_t Height)
{
	uint16_t i,j,x,y;
	 y=Ypos;
	for(i=0;i<Height+1;i++)
	{
		x=Xpos;
		for(j=0;j<Width+1;j++)
		{
			if(CurrentLayer == LCD_FOREGROUND_LAYER)
			*(__IO uint16_t*)(CurrentFrameBuffer + 2*( x+y*800)) = color_trans(CurrentTextColor);    
			else *(__IO uint16_t*)(CurrentFrameBuffer + 2*(x+y*800)) = CurrentTextColor;
			x++;
		}
		y++;
	}
}

void plot_pole(uint16_t sh)
{
	uint16_t temp1,temp2;
    if(sh&pole_sta)
	{
		if(pole_dir==0) {temp1=BUTTON_COLOR; temp2=BUTTON_COLOR;}
		else if(pole_dir==1) {temp1=LCD_COLOR_BLUE; temp2=LCD_COLOR_RED;}
		else if(pole_dir==2) {temp1=LCD_COLOR_RED; temp2=LCD_COLOR_BLUE;}
    }
    else { temp1=0x7390; temp2=0x7390; }
    switch(sh)
    {
        case 0x8000: LCD_SetTextColor(temp1); LCD_DrawFullCircle(135,125,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(113,125,1,(uint8_t*)"L1");
                     break;
        case 0x4000: LCD_SetTextColor(temp1); LCD_DrawFullCircle(195,125,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(113,185,1,(uint8_t*)"L2");
                     break;
        case 0x2000: LCD_SetTextColor(temp1); LCD_DrawFullCircle(255,125,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(113,245,1,(uint8_t*)"L3");
                     break;
        case 0x1000: LCD_SetTextColor(temp1); LCD_DrawFullCircle(305,125,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(113,295,1,(uint8_t*)"L4");
                     break;
        case 0x0800: LCD_SetTextColor(temp1); LCD_DrawFullCircle(135,185,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(173,125,1,(uint8_t*)"L5");
                     break;
        case 0x0400: LCD_SetTextColor(temp1); LCD_DrawFullCircle(195,185,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(173,185,1,(uint8_t*)"L6");
                     break;
        case 0x0200: LCD_SetTextColor(temp1); LCD_DrawFullCircle(255,185,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(173,245,1,(uint8_t*)"L7");
                     break;
        case 0x0100: LCD_SetTextColor(temp1); LCD_DrawFullCircle(305,185,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(173,295,1,(uint8_t*)"L8");
                     break;
        case 0x0080: LCD_SetTextColor(temp2); LCD_DrawFullCircle(490,125,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(113,480,1,(uint8_t*)"R1");
                     break;
        case 0x0040: LCD_SetTextColor(temp2); LCD_DrawFullCircle(550,125,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(113,540,1,(uint8_t*)"R2");
                     break;
        case 0x0020: LCD_SetTextColor(temp2); LCD_DrawFullCircle(612,125,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(113,602,1,(uint8_t*)"R3");
                     break;
        case 0x0010: LCD_SetTextColor(temp2); LCD_DrawFullCircle(670,125,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(113,660,1,(uint8_t*)"R4");
                     break;
        case 0x0008: LCD_SetTextColor(temp2); LCD_DrawFullCircle(490,185,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(173,480,1,(uint8_t*)"R5");
                     break;
        case 0x0004: LCD_SetTextColor(temp2); LCD_DrawFullCircle(550,185,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(173,540,1,(uint8_t*)"R6");
                     break;
        case 0x0002: LCD_SetTextColor(temp2); LCD_DrawFullCircle(610,185,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(173,600,1,(uint8_t*)"R7");
                     break;
        case 0x0001: LCD_SetTextColor(temp2); LCD_DrawFullCircle(670,185,18);
                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(173,660,1,(uint8_t*)"R8");
                     break;
    }
//    switch(sh)
//    {
//        case 0x8000: LCD_SetTextColor(temp1); LCD_DrawFullCircle(174,125,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(113,161,1,(uint8_t*)"L1");
//                     break;
//        case 0x4000: LCD_SetTextColor(temp1); LCD_DrawFullCircle(226,125,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(113,214,1,(uint8_t*)"L2");
//                     break;
//        case 0x2000: LCD_SetTextColor(temp1); LCD_DrawFullCircle(278,125,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(113,267,1,(uint8_t*)"L3");
//                     break;
//        case 0x1000: LCD_SetTextColor(temp1); LCD_DrawFullCircle(122,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(172,111,1,(uint8_t*)"L4");
//                     break;
//        case 0x0800: LCD_SetTextColor(temp1); LCD_DrawFullCircle(174,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(173,162,1,(uint8_t*)"L5");
//                     break;
//        case 0x0400: LCD_SetTextColor(temp1); LCD_DrawFullCircle(226,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(173,214,1,(uint8_t*)"L6");
//                     break;
//        case 0x0200: LCD_SetTextColor(temp1); LCD_DrawFullCircle(278,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(173,267,1,(uint8_t*)"L7");
//                     break;
//        case 0x0100: LCD_SetTextColor(temp1); LCD_DrawFullCircle(330,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp1);LCD_DisplayStringLine(173,319,1,(uint8_t*)"L8");
//                     break;
//        case 0x0080: LCD_SetTextColor(temp2); LCD_DrawFullCircle(522,125,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(113,509,1,(uint8_t*)"R1");
//                     break;
//        case 0x0040: LCD_SetTextColor(temp2); LCD_DrawFullCircle(574,125,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(113,562,1,(uint8_t*)"R2");
//                     break;
//        case 0x0020: LCD_SetTextColor(temp2); LCD_DrawFullCircle(626,125,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(113,616,1,(uint8_t*)"R3");
//                     break;
//        case 0x0010: LCD_SetTextColor(temp2); LCD_DrawFullCircle(470,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(172,459,1,(uint8_t*)"R4");
//                     break;
//        case 0x0008: LCD_SetTextColor(temp2); LCD_DrawFullCircle(522,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(173,510,1,(uint8_t*)"R5");
//                     break;
//        case 0x0004: LCD_SetTextColor(temp2); LCD_DrawFullCircle(574,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(173,562,1,(uint8_t*)"R6");
//                     break;
//        case 0x0002: LCD_SetTextColor(temp2); LCD_DrawFullCircle(626,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(173,615,1,(uint8_t*)"R7");
//                     break;
//        case 0x0001: LCD_SetTextColor(temp2); LCD_DrawFullCircle(678,185,18);
//                     LCD_SetColors(LCD_COLOR_WHITE ,temp2);LCD_DisplayStringLine(173,667,1,(uint8_t*)"R8");
//                     break;
//    }
}

void show_pole(void)
{
	uint16_t i,sh;
	for(i=0;i<16;i++)
	{
		sh=1<<i;
		plot_pole(sh);
	}
}

void draw_fun_icon(void)
{
	LCD_SetColors(ICON_COLOR,BACK_WORK_COLOR);
	put_icon(25,436,LOGIN,ICON_COLOR);
	put_icon(130,436,DJ,ICON_COLOR);
	put_icon(236,436,CJ,ICON_COLOR);
	put_icon(382,436,CAS,ICON_COLOR);
	//LCD_DisplayStringLine(440,58,0,(uint8_t*)"��¼");
	if(UI==1) 	LCD_DisplayStringLine(440,162,1,(uint8_t*)"�缫");
	else LCD_DisplayStringLine(440,162,0,(uint8_t*)"�缫");
	if(UI==2) LCD_DisplayStringLine(440,280,1,(uint8_t*)"Ԥ�̼�");
	else LCD_DisplayStringLine(440,280,0,(uint8_t*)"Ԥ�̼�");
	if(UI==3) LCD_DisplayStringLine(440,424,1,(uint8_t*)"����");
	else LCD_DisplayStringLine(440,424,0,(uint8_t*)"����");
	LCD_SetTextColor(0x1401);
	fill_rect(630,420,170,60);
	LCD_SetColors(LCD_COLOR_WHITE ,0x1401);
	if(UI==3)
		LCD_DisplayStringLine(442,685,1,(uint8_t*)"��  ʼ");
	else
		LCD_DisplayStringLine(442,685,1,(uint8_t*)"ȷ  ��");
}

void face_pole_init(void)
{
	UI=1;	
	set_layer(FOREG_BUF);
	screen_clear(LCD_COLOR_WHITE);	
	set_layer(BACK2_BUF);                      //����������2

	LCD_SetTextColor(0x1108);//������ʾ������
	fill_rect(170,320,44,50);
	fill_rect(236,320,44,50);
	fill_rect(575,320,44,50);
	fill_rect(641,320,44,50);
	LCD_SetTextColor(LCD_COLOR_RED);
	fill_rect(178,342,28,4);
	fill_rect(583,342,28,4);
	fill_rect(190,329,4,30);
	fill_rect(595,329,4,30);
	LCD_SetTextColor(LCD_COLOR_BLUE2);
	fill_rect(246,342,24,4);
	fill_rect(651,342,24,4);
	
	show_pole();
	change_backdrop(BACK2_BUF);                    //�л���������2���缫���ý��棩
	set_layer(FOREG_BUF);                       //����ǰ����
	LCD_SetTextColor(0x1108);
	LCD_DisplayStringLine(332,92,0,(uint8_t*)"���");
	LCD_DisplayStringLine(332,497,0,(uint8_t*)"�Ҳ�");
	
	LCD_SetTextColor(BACK_WORK_COLOR);
	fill_rect(X_dj_L,Y_icon_T ,105,60);
	
	draw_fun_icon();
}

void plot_case_name(void)
{
	uint8_t k,i,j,hp,syb;
	uint8_t tempbuf[9]={0};
	
	LCD_SetTextColor(BACK_COLOR);
	fill_rect(120,60,560,200);           //�����������ʾ
	
    w25qxx_read_buf(casebuf_rec,FCASE_ADD,256);
    
	if(casebuf_rec[2]==0||casebuf_rec[2]==0xFF)
	{
		LCD_SetColors(LCD_COLOR_RED ,BACK_COLOR);
		LCD_DisplayStringLine(76,142,1,(uint8_t*)"�洢����!");
		syb=0;
		hp=0;
	}
	else	
	{
		hp=casebuf_rec[2]-page*5;
		syb=1;
	}
	
	if(hp>5)
	{
		down=1;
		put_icon(564,180,ZDYBACK,0x1401);
		LCD_SetColors(ICON_COLOR ,0x1401);
	}
	else 
	{
		down=0;
		put_icon(564,180,ZDYBACK,LCD_COLOR_GREY);
		LCD_SetColors(ICON_COLOR ,LCD_COLOR_GREY);
	}
	LCD_DisplayStringLine(188,576,1,(uint8_t*)"��һҳ");
	
	if(page)
	{
		up=1;
		put_icon(564,100,ZDYBACK,0x1401);
		LCD_SetColors(ICON_COLOR ,0x1401);
	}
	else
	{
		up=0;
		put_icon(564,100,ZDYBACK,LCD_COLOR_GREY);
		LCD_SetColors(ICON_COLOR ,LCD_COLOR_GREY);
	}
	LCD_DisplayStringLine(108,576,1,(uint8_t*)"��һҳ");
	
	if(hp>5||hp==0) hp=5;                 //���hp=0��������ҳ����hp>5����ʾ��ҳ��������һҳ
	
	if(syb)                                  //����洢���з���
	{
		if(colu)
		{
			LCD_SetTextColor(BACK_WORK_COLOR);
			fill_rect(130,70+(colu-1)*36,360,36);
		}
			
		if(casebuf_rec[2]>0)
		{
			k=76;
			tempbuf[8]=0;
			for(i=0;i<hp;i++)
			{
				for(j=0;j<8;j++)
				{
					tempbuf[j]=casebuf_rec[j+(page*5+i)*16+0x20];
				}
				if(colu)
				{
					if(((k-76)/36)==(colu-1))
						LCD_SetColors(ICON_COLOR ,BACK_WORK_COLOR);
					else
						LCD_SetColors(ICON_COLOR ,BACK_COLOR);
				}
				else
					LCD_SetColors(ICON_COLOR ,BACK_COLOR);
				
				LCD_DisplayStringLine(k,142,1,(uint8_t*)tempbuf);
				k+=36;
			}
		}
	}
}

void face_mycase_op_init(void)
{
	UI=7;
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK3_BUF);

	LCD_SetTextColor(0x198C);
	fill_rect(X_zdy_LS,Y_icon_T ,140,60);
    fill_rect(X_zdy_LF,Y_icon_T ,139,60);
	LCD_SetTextColor(0x1401);
	fill_rect(630,420,170,60);
	LCD_SetColors(LCD_COLOR_WHITE ,0x1401);
	LCD_DisplayStringLine(442,685,1,(uint8_t*)"ȷ ��");
	LCD_SetColors(LCD_COLOR_WHITE ,0x198C);
    LCD_DisplayStringLine(441,392,1,(uint8_t*)"�� ��");
    LCD_DisplayStringLine(441,532,1,(uint8_t*)"ɾ ��");
	
	plot_case_name();
}

void face_ycj_init(void)
{
	UI=2;
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK3_BUF);                            //�л����̼��������ñ�������;��
	LCD_SetTextColor(BACK_WORK_COLOR);
	fill_rect(X_cj_L,Y_icon_T ,141,60);
	draw_fun_icon();
	LCD_SetTextColor(LCD_COLOR_WHITE);
	LCD_DisplayStringLine(100,192,0,(uint8_t*)"Ԥ�̼�ģʽ�������������ǿ�Ⱥ�ȷ����");
	put_icon(122,240,RATE_L,0x3564);                      //���ý��������
	if(current) plot_buoy();
	else
	{
        buoy_seat=BUOY_S;
		put_icon(buoy_seat,182,BUOY,0x3564);            //���ø���
		fill_rect(130,240 ,513,32);                     //���ý�����
        drow_ball(122,234,1);
		LCD_SetColors(LCD_COLOR_WHITE,0x3564);
		LCD_DisplayStringLine(188,132,1,(uint8_t*)"0��A");
	}
    
	LCD_DisplayStringLine(244,72,0,(uint8_t*)"0��A");
	LCD_DisplayStringLine(244,674,0,(uint8_t*)"2mA");     //������ָʾ
	put_icon(643,240,RATE_R,LCD_COLOR_WHITE);             //���ý������Ҷ�
}

void draw_cas_icon(uint8_t symocase,uint16_t color)
{
	switch(symocase)
	{
		case 1: put_icon(107,160,LISTCA,color);
		        LCD_DisplayStringLine(176,145,1,(uint8_t*)"����");
		        LCD_DisplayStringLine(176,195,1,(uint8_t*)"1");
		        break;
		case 2: put_icon(257,160,LISTCA,color);
		        LCD_DisplayStringLine(176,295,1,(uint8_t*)"����");
		        LCD_DisplayStringLine(176,345,1,(uint8_t*)"2");
		        break;
		case 3: put_icon(407,160,LISTCA,color);
		        LCD_DisplayStringLine(176,445,1,(uint8_t*)"����");
		        LCD_DisplayStringLine(176,495,1,(uint8_t*)"3");
		        break;
		case 4: put_icon(557,160,LISTCA,color);
		        LCD_DisplayStringLine(176,595,1,(uint8_t*)"����");
		        LCD_DisplayStringLine(176,645,1,(uint8_t*)"4");
		        break;
		case 5: put_icon(107,252,LISTCA,color);
		        LCD_DisplayStringLine(268,145,1,(uint8_t*)"����");
		        LCD_DisplayStringLine(268,195,1,(uint8_t*)"5");
		        break;
		case 6: put_icon(257,252,LISTCA,color);
		        LCD_DisplayStringLine(268,295,1,(uint8_t*)"����");
		        LCD_DisplayStringLine(268,345,1,(uint8_t*)"6");
						break;
		case 7: put_icon(407,252,LISTCA,color);
		        put_icon(414,268,SAVE,ICON_COLOR);
		        LCD_DisplayStringLine(268,442,1,(uint8_t*)"�ҵķ���");
						break;
		case 8: put_icon(557,252,LISTCA,color);
		        LCD_DisplayStringLine(268,607,1,(uint8_t*)"�Զ���");
						put_icon(578,268,ZDY,ICON_COLOR);
						break;
	}
}


void face_case_init(void)
{
	uint8_t i;
	UI=3;
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK3_BUF);                   //�л��������������ñ�������;��
	LCD_SetTextColor(LCD_COLOR_WHITE );
	LCD_DisplayStringLine(100,305,0,(uint8_t*)"��ѡ��̼�����");
	LCD_SetTextColor(BACK_WORK_COLOR);
	fill_rect(X_case_L,Y_icon_T ,125,60);
	draw_fun_icon();
	LCD_SetColors(ICON_COLOR,BACK_COLOR);
	for(i=1;i<9;i++)
	{
		if(fcase==i) 
		{
			LCD_SetColors(ICON_COLOR,0x1401);         //��ѡ��ɫ
			draw_cas_icon(fcase,0x1401);
			LCD_SetColors(ICON_COLOR,BACK_COLOR);      //�ָ�δѡ��ɫ
		}
		else	draw_cas_icon(i,BACK_COLOR);
	}
}

void pitch_pad(uint8_t pad)
{
	uint8_t i,z;
	uint16_t dx,dy;
	
	if(inbox) 
		z=inbox-1;
	if(pad<13)                          //��������ּ�����
	{
		LCD_SetColors(ICON_COLOR ,BACK_WORK_COLOR);
		dx=(pad-1)%3;            //������
		dy=(pad-1)/3;            //������
		put_icon(555+dx*64,90+dy*66,PAD_DT,BACK_WORK_COLOR);
		if(pad==10) put_icon(561,305,BACK,ICON_COLOR);
		else if(pad==12)
		{
			LCD_SetFont(16);
			LCD_DisplayStringLine(309,691,1,(uint8_t*)"����");
			LCD_SetFont(24);
		}
		else
		{
			dot_chr[1]=0;
			if(pad==11) dot_chr[0]=48;
			else dot_chr[0]=pad+48;
			LCD_DisplayStringLine(105+dy*66,573+dx*64,1,(uint8_t*)dot_chr);
		}
	}
	else if(pad<18) z=pad-13;
	if(inbox||(pad>12&&pad<17))
	{
		LCD_SetTextColor(ICON_COLOR);
		for(i=0;i<cursor_seat[z];i++)               
		{
			dot_chr[i]=boxdata[z][i]+47;      //��д�ַ�
		}
		dot_chr[cursor_seat[z]]=0;          //��괦����ʾ�ַ�
		LCD_DisplayStringLine(111+z*60,232,0,(uint8_t*)"      ");   //ɾ���ַ���ʾ
		LCD_DisplayStringLine(111+z*60,232,0,(uint8_t*)dot_chr);  
	}
	if(pad<13)                          //��������ּ�����,����ʱ��ɾ��
	{
		HAL_Delay(120);
		LCD_SetColors(ICON_COLOR ,BACK_WORK_COLOR);
		Lcd_clear_rect(555+dx*64,90+dy*66,52,56);
	}
}

void plot_cursor(uint8_t del)
{
	uint16_t temp;
	uint8_t z,r;
	if(UI==5)
	{
		if(inbox)
		{
			r=inbox-1;
			z=cursor_seat[inbox-1];
			if(z) temp=236;
			else temp=232;
			if(del) fill_rect(temp+z*12,113+r*60,2,21);
			else Lcd_clear_rect(temp+z*12,113+r*60,2,21);
		}
	}
	else if(UI==4)
	{
		if(del) fill_rect(146+key_num*12,74,2,21);
		else Lcd_clear_rect(146+key_num*12,74,2,21);
	}
}

void face_mydef_init(void)
{
	uint8_t i,j;
	UI=5;
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK1_BUF);                   //�л����Զ��巽������
	set_layer(BACK1_BUF);
	LCD_SetColors(ICON_COLOR,BACK_COLOR);
	dot_chr[1]=0;
	for(i=0;i<3;i++)
	{
		for(j=1;j<4;j++)
		{
			dot_chr[0]=(j+i*3)+48;
			LCD_DisplayStringLine(105+i*66,573+(j-1)*64,1,(uint8_t*)dot_chr);
		}
	}
	put_icon(561,305,BACK,ICON_COLOR);
	dot_chr[0]=48;
	LCD_DisplayStringLine(304,636,1,(uint8_t*)dot_chr);
	LCD_SetFont(16);
	LCD_DisplayStringLine(309,691,1,(uint8_t*)"����");
	LCD_SetFont(24);
	
	set_layer(FOREG_BUF);
	LCD_DisplayStringLine(42,236,0,(uint8_t*)"�Զ��巽��");
	
	LCD_DisplayStringLine(111,122,0,(uint8_t*)"����ʱ��");
	LCD_DisplayStringLine(171,122,0,(uint8_t*)"ƽ̨ʱ��");
	LCD_DisplayStringLine(231,122,0,(uint8_t*)"��Ъʱ��");
	LCD_DisplayStringLine(291,122,0,(uint8_t*)"�½�ʱ��");
	LCD_DisplayStringLine(349,122,0,(uint8_t*)"�̼�ʱ��");
	LCD_DisplayStringLine(111,397,0,(uint8_t*)"��");
	LCD_DisplayStringLine(171,397,0,(uint8_t*)"����");
	LCD_DisplayStringLine(231,397,0,(uint8_t*)"��");
	LCD_DisplayStringLine(291,397,0,(uint8_t*)"��");
	LCD_DisplayStringLine(349,397,0,(uint8_t*)"����");
	
	LCD_SetTextColor(BACK_COLOR);
	fill_rect(0,Y_icon_T ,490,60);
	LCD_SetTextColor(0x198C);
	fill_rect(X_zdy_LS,Y_icon_T ,140,60);
    fill_rect(X_zdy_LF,Y_icon_T ,139,60);
	LCD_SetTextColor(0x1401);
	fill_rect(630,420,170,60);
	
	LCD_SetColors(LCD_COLOR_WHITE ,0x1401);
	LCD_DisplayStringLine(442,685,1,(uint8_t*)"ȷ ��");
	LCD_SetColors(LCD_COLOR_WHITE ,0x198C);
	LCD_DisplayStringLine(441,532,1,(uint8_t*)"�� ��");
    LCD_DisplayStringLine(441,392,1,(uint8_t*)"�� ��");
	
	for(i=0;i<5;i++)
	{
		pitch_pad(i+13);          //��ʾ�������ݣ����еĻ���
	}
	plot_cursor(1);             //��ʾ��꣨���еĻ���
}


void login_init(void)
{
	uint8_t i,j;
	UI=10;
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK1_BUF);                   //�л����Զ��巽������
	set_layer(BACK1_BUF);
	LCD_SetColors(ICON_COLOR,BACK_COLOR);
	dot_chr[1]=0;
	for(i=0;i<3;i++)
	{
		for(j=1;j<4;j++)
		{
			dot_chr[0]=(j+i*3)+48;
			LCD_DisplayStringLine(105+i*66,573+(j-1)*64,1,(uint8_t*)dot_chr);
		}
	}
	put_icon(561,305,BACK,ICON_COLOR);
	dot_chr[0]=48;
	LCD_DisplayStringLine(304,636,1,(uint8_t*)dot_chr);
	LCD_SetFont(16);
	LCD_DisplayStringLine(309,691,1,(uint8_t*)"����");
	LCD_SetFont(24);
	
	set_layer(FOREG_BUF);
	LCD_DisplayStringLine(42,236,0,(uint8_t*)"��¼ϵͳ");
	
	LCD_DisplayStringLine(111,122,0,(uint8_t*)"����");
	
	LCD_DisplayStringLine(441,392,0,(uint8_t*)"�汾V1.0");
	
	LCD_SetColors(0x0555 ,0x0555);
	fill_rect(230,105,75,36);/////////////////////////////////////////
	
	LCD_SetColors(LCD_COLOR_WHITE ,0x1401);
	LCD_DisplayStringLine(442,685,1,(uint8_t*)"�� ¼");
	LCD_SetColors(LCD_COLOR_WHITE ,0x198C);
	LCD_DisplayStringLine(441,532,1,(uint8_t*)"�޸�����");
	
	
	for(i=0;i<1;i++)               //����������Ļ���Ϊ1
	{
		pitch_pad(i+13);          //��ʾ�������ݣ����еĻ���
	}
	plot_cursor(1);             //��ʾ��꣨���еĻ���
}

void modifi_init(void)
{
	uint8_t i,j;
	UI=11;
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK1_BUF);                   //�л����Զ��巽������
	set_layer(BACK1_BUF);
	LCD_SetColors(ICON_COLOR,BACK_COLOR);
	dot_chr[1]=0;
	for(i=0;i<3;i++)
	{
		for(j=1;j<4;j++)
		{
			dot_chr[0]=(j+i*3)+48;
			LCD_DisplayStringLine(105+i*66,573+(j-1)*64,1,(uint8_t*)dot_chr);
		}
	}
	put_icon(561,305,BACK,ICON_COLOR);
	dot_chr[0]=48;
	LCD_DisplayStringLine(304,636,1,(uint8_t*)dot_chr);
	LCD_SetFont(16);
	LCD_DisplayStringLine(309,691,1,(uint8_t*)"����");
	LCD_SetFont(24);
	
	set_layer(FOREG_BUF);
	LCD_DisplayStringLine(42,236,0,(uint8_t*)"�޸�����");
	
	LCD_DisplayStringLine(111,122,0,(uint8_t*)"ԭ����");
	LCD_DisplayStringLine(171,122,0,(uint8_t*)"������");
	LCD_SetColors(0x0555 ,0x0555);
	fill_rect(230,105,75,36);
	fill_rect(230,165,75,36);/////////////////////////////////////////////////////////////////////////
	
	
	LCD_SetColors(LCD_COLOR_WHITE ,0x1401);
	LCD_DisplayStringLine(442,685,1,(uint8_t*)"�޸�");
	
	
	for(i=0;i<2;i++)               //����������Ļ���Ϊ1
	{
		pitch_pad(i+13);          //��ʾ�������ݣ����еĻ���
	}
	plot_cursor(1);             //��ʾ��꣨���еĻ���
}



static void plot_key(uint8_t num,uint16_t color)
{
	dot_chr[1]=0;
	dot_chr[0]=key_chr[num];
	LCD_SetColors(ICON_COLOR ,color);
	if(num==29)
	{
		put_icon(keyx[num],keyy[num],ZDYBACK,color);
		put_icon(keyx[num]+32,keyy[num]+8,BACK,ICON_COLOR);
	}
	else if(num==37)	
	{
		if(color==BACK_WORK_COLOR) put_icon(keyx[num],keyy[num],ZDYBACK,0x1401);
		else	put_icon(keyx[num],keyy[num],ZDYBACK,0x1341);  
		put_icon(keyx[num]+36,keyy[num]+8,FIND,ICON_COLOR);
	}			
	else
	{
		put_icon(keyx[num],keyy[num],KEY,color);
		LCD_DisplayStringLine(keyy[num]+8,keyx[num]+22,1,(uint8_t*)dot_chr);
	}
}

void face_def_name_init(void)
{
	uint8_t i;
	UI=4;
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK4_BUF);                   //�л����Ѵ淽������
	for(i=0;i<38;i++)
	{
		plot_key(i,BACK_WORK_COLOR);
	}
	LCD_SetColors(ICON_COLOR ,0x198C);            //��������ɫ��ȷ
	LCD_DisplayStringLine(145,584,0,(uint8_t*)"ȷ ��");
	if(key_num)                            //������ַ�
	{
		pitch_key(50);                       //��������39�򲻴���������ʾ������ʾ�ַ�
	}
	plot_cursor(1);                        //��ʾ���
}

void pitch_key(uint8_t num)             //�������̰�������
{
	uint8_t i;
	if(num<39) plot_key(num,BACK_COLOR);
	for(i=0;i<key_num;i++)
	{
		dot_chr[i]=fcas[i];
	}
	dot_chr[key_num]=0;
	LCD_DisplayStringLine(72,142,0,(uint8_t*)"            ");
	LCD_DisplayStringLine(72,142,0,(uint8_t*)dot_chr);
	for(i=0;i<8;i++)
	{
		dot_chr[i]=0;
	}
	HAL_Delay(120);
	if(num
        <39) plot_key(num,BACK_WORK_COLOR);
}

void save_mydef_init(void)
{
	uint8_t i;
	UI=6;
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK3_BUF);
	LCD_SetTextColor(BACK_COLOR);
	fill_rect(120,60,560,150);
	LCD_SetColors(ICON_COLOR ,BACK_COLOR);
	LCD_DisplayStringLine(72,142,1,(uint8_t*)"��������");
	for(i=0;i<key_num;i++)
	{
		dot_chr[i]=fcas[i];
	}
	dot_chr[key_num]=0;
	LCD_DisplayStringLine(72,238,1,(uint8_t*)dot_chr);
	
	LCD_SetTextColor(0x198C);
	fill_rect(X_zdy_LS,Y_icon_T ,140,60);
	LCD_SetTextColor(0x1401);
	fill_rect(630,420,170,60);
	
	LCD_SetColors(LCD_COLOR_WHITE ,0x1401);
	LCD_DisplayStringLine(442,685,1,(uint8_t*)"�� ��");
	LCD_SetColors(LCD_COLOR_WHITE ,0x198C);
	LCD_DisplayStringLine(441,532,1,(uint8_t*)"�� ��");
}

uint8_t work_cun=0,time_ch[9]={48,48,58,48,48,58,48,48,0};
uint16_t work_time=0,record=0;

void face_work_init(void)
{
	UI=8;
	if(work_cun==0)                  //������½���
	{
		work_time=0;
		record=0;
		time_ch[0]=48;
		time_ch[1]=48;
		time_ch[2]=58;
		time_ch[3]=48;
		time_ch[4]=48;
		time_ch[5]=58;
		time_ch[6]=48;
		time_ch[7]=48;
		time_ch[8]=0;
	}
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK3_BUF);
	put_icon(582,12,WORK,BACK_COLOR);//�ױߵĳ���
	LCD_SetTextColor(LCD_COLOR_RED);
	fill_rect(315,420,170,60);
	LCD_SetColors(ICON_COLOR ,LCD_COLOR_RED);
	LCD_DisplayStringLine(440,370,1,(uint8_t*)"ͣ ֹ");
	LCD_SetColors(ICON_COLOR ,BACK_COLOR);
	LCD_DisplayStringLine(30,638,1,(uint8_t*)"����ʱ��");
	LCD_DisplayStringLine(68,636,1,(uint8_t*)time_ch);
	fill_rect(602,110,166,10);

	LCD_DisplayStringLine(150,638,1,(uint8_t*)"��������");//��ʾ��������
	LCD_DisplayStringLine(188,646,1,(uint8_t*)do_ch);//sysoper��143��
	LCD_DisplayStringLine(188,695,1,(uint8_t*)"��A");

}

uint16_t BUI=0;
extern uint8_t err_code;
void face_war_init(uint8_t msg)
{
	BUI=UI;
	UI=9;
	screen_clear(LCD_COLOR_WHITE);
	change_backdrop(BACK3_BUF);
	LCD_SetTextColor(LCD_COLOR_WHITE);
	fill_rect(93,65,614,350);
	DMA2D_Copy(BOTT_PIC,FOREG_BUF);
	DMA2D_Copy(WAR_PIC,FOREG_BUF);
	
	LCD_SetTextColor(0x1401);
	fill_rect(325,306,170,60);
	LCD_SetColors(LCD_COLOR_WHITE,0x1401);
	LCD_DisplayStringLine(324,380,1,(uint8_t*)"ȷ ��");
	
	LCD_SetColors(LCD_COLOR_RED,LCD_COLOR_WHITE);
    LCD_DisplayStringLine(178,310,1,(uint8_t*)"������룺");
    dot_chr[0]=(err_code/10)+48;
    dot_chr[1]=err_code%10+48;
    dot_chr[2]=0;
    LCD_DisplayStringLine(178,430,1,(uint8_t*)dot_chr);
	switch(msg)
	{
		case 0: LCD_DisplayStringLine(144,310,1,(uint8_t*)"�缫�Ӵ�����������ͣ��");
				LCD_DisplayStringLine(180,310,1,(uint8_t*)"���������缫��");          break;
		case 1: LCD_DisplayStringLine(144,310,1,(uint8_t*)"��û��Ȩ��ʹ�øù��ܣ�");    break;
		case 2: LCD_DisplayStringLine(144,310,1,(uint8_t*)"�缫δ����������");          break;
		case 3: LCD_DisplayStringLine(144,310,1,(uint8_t*)"δ���õ缫���ԣ�");          break;
		case 4: LCD_DisplayStringLine(144,310,1,(uint8_t*)"�缫δ���û�������");      break;
		case 5: LCD_DisplayStringLine(144,310,1,(uint8_t*)"����Դ̼�ǿ�ȣ�");          break;
		case 6: LCD_DisplayStringLine(144,310,1,(uint8_t*)"����ʱ�䲻��Ϊ0��");         break;
		case 7: LCD_DisplayStringLine(144,310,1,(uint8_t*)"�̼�ʱ�䲻��Ϊ0��");         break;
		case 8: LCD_DisplayStringLine(144,310,1,(uint8_t*)"��һ�洢��������");          break;
		case 9: LCD_DisplayStringLine(144,310,1,(uint8_t*)"δѡ�񷽰���");              break;
		case 10:LCD_DisplayStringLine(144,310,1,(uint8_t*)"�缫�迹���ͣ����ų���");    break;
		case 11:LCD_DisplayStringLine(144,310,1,(uint8_t*)"�缫�����г�ͻ��");          break;
		case 12:LCD_DisplayStringLine(144,310,1,(uint8_t*)"�缫���䣡");                break;
		case 13:LCD_DisplayStringLine(144,310,1,(uint8_t*)"ͨѶ����");                  break;
		case 14:LCD_DisplayStringLine(144,310,1,(uint8_t*)"DACоƬ��ַ����");         break;
		case 15:LCD_DisplayStringLine(144,310,1,(uint8_t*)"DACоƬ��д����");         break;
		case 16:LCD_DisplayStringLine(144,310,1,(uint8_t*)"�迹�����������ޣ�");    break;
		case 17:LCD_DisplayStringLine(144,310,1,(uint8_t*)"���������������ޣ�");    break;
		case 18:LCD_DisplayStringLine(144,310,1,(uint8_t*)"�������δ��ɣ����Ժ�");  break;
		case 19:LCD_DisplayStringLine(144,310,1,(uint8_t*)"������� ");  break;
	}
}

void replace_work(void)
{
	uint16_t temp,temps;
	work_time++;
	time_ch[7]++;
	if(time_ch[7]>57) 
	{
		time_ch[7]=48;
		time_ch[6]++;
		if(time_ch[6]>53)             //��ʮλ���ܴ���6
		{
			time_ch[6]=48;
			time_ch[4]++;
		}
		if(time_ch[4]>57)
		{
			time_ch[4]=48;
			time_ch[3]++;
		}	
		if(time_ch[3]>53)             //��ʮλ���ܴ���6
		{
			time_ch[3]=48;
			time_ch[1]++;
		}
		if(time_ch[1]>57)
		{
			time_ch[1]=48;
			time_ch[0]++;
		}
		if(time_ch[0]>49)            //Сʱʮλ���ܴ���2
			time_ch[0]=48;
	}
	LCD_SetColors(ICON_COLOR ,BACK_COLOR);
	LCD_DisplayStringLine(68,636,1,(uint8_t*)time_ch);
	temp=temps=(((uint16_t)ctrldata[6])<<8)+ctrldata[7];
	temp/=83;
	if((work_time-record)>temp)
	{
		record=work_time;
		LCD_SetTextColor(0x1401);
		fill_rect(602+work_cun,110,2,10);
		work_cun+=2;
	}
    if(work_time>temps)
    {
        stop_out();
        face_pole_init();
    }        
}

