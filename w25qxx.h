

#ifndef __W25QXX_H
#define __W25QXX_H	

#define W25Q80 	0XEF13 	
#define W25Q16 	0XEF14
#define W25Q32 	0XEF15
#define W25Q64 	0XEF16
#define W25Q128	0XEF17
#define W25Q256 0XEF18

#define FCASE_ADD  4096
#define FLASH_BASE_ADDR  0x100000


void w25qxx_get_jedec(uint8_t* idbuf);
void w25q_erase_chip(void);
void w25q_erast_sector(uint32_t sector);
uint8_t w25qxx_write_buf(uint8_t* buf, uint32_t addr, uint32_t size);
void w25qxx_read_buf(uint8_t* buf, uint32_t addr, uint16_t bytelen);
void w25qxx_init(void);


#endif
