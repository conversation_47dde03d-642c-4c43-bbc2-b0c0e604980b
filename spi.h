/**
  ******************************************************************************
  * File Name          : SPI.h
  * Description        : This file provides code for the configuration
  *                      of the SPI instances.
  ******************************************************************************/

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __spi_H
#define __spi_H
#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"

/* USER CODE BEGIN Includes */

/* USER CODE END Includes */


/* USER CODE BEGIN Private defines */
#define  STD_SPI_MODE
/* USER CODE END Private defines */

/*******   define qspi pin port and pin   *******/
#define  S_CS_PORT     GPIOE
#define  QSPI_PORT     GPIOB
    
#define  S_CS_PIN      2
#define  S_CK_PIN      3
#define  S_D0_PIN      4
#define  S_D1_PIN      5
#define  S_D2_PIN      6
#define  S_D3_PIN      7
//------------------------------------------------------------    
/*******   operation qspi pin   *******/
#define  SET_CS_H      S_CS_PORT->ODR |= 1<<S_CS_PIN
#define  SET_CS_L      S_CS_PORT->ODR &= ~(1<<S_CS_PIN)  

#define  SET_CK_H      QSPI_PORT->ODR |= 1<<S_CK_PIN
#define  SET_CK_L      QSPI_PORT->ODR &= ~(1<<S_CK_PIN)

#define  SET_D0_H      QSPI_PORT->ODR |= 1<<S_D0_PIN
#define  SET_D0_L      QSPI_PORT->ODR &= ~(1<<S_D0_PIN)

#define  SET_D1_H      QSPI_PORT->ODR |= 1<<S_D1_PIN
#define  SET_D1_L      QSPI_PORT->ODR &= ~(1<<S_D1_PIN)

#define  SET_D2_H      QSPI_PORT->ODR |= 1<<S_D2_PIN
#define  SET_D2_L      QSPI_PORT->ODR &= ~(1<<S_D2_PIN)

#define  SET_D3_H      QSPI_PORT->ODR |= 1<<S_D3_PIN
#define  SET_D3_L      QSPI_PORT->ODR &= ~(1<<S_D3_PIN)

#define  SET_D0_PH     QSPI_PORT->PUPDR |= 1<< (S_D0_PIN<<1)
#define  SET_D1_PH     QSPI_PORT->PUPDR |= 1<< (S_D1_PIN<<1)
#define  SET_D2_PH     QSPI_PORT->PUPDR |= 1<< (S_D2_PIN<<1)
#define  SET_D3_PH     QSPI_PORT->PUPDR |= 1<< (S_D3_PIN<<1)

#define  SET_D0_PN     QSPI_PORT->PUPDR &= 3<< (S_D0_PIN<<1)
#define  SET_D1_PN     QSPI_PORT->PUPDR &= 3<< (S_D1_PIN<<1)
#define  SET_D2_PN     QSPI_PORT->PUPDR &= 3<< (S_D2_PIN<<1)
#define  SET_D3_PN     QSPI_PORT->PUPDR &= 3<< (S_D3_PIN<<1)

#define  SET_D0_OD     QSPI_PORT->OTYPER |= (1<<S_D0_PIN)
#define  SET_D1_OD     QSPI_PORT->OTYPER |= (1<<S_D1_PIN)
#define  SET_D2_OD     QSPI_PORT->OTYPER |= (1<<S_D2_PIN)
#define  SET_D3_OD     QSPI_PORT->OTYPER |= (1<<S_D3_PIN)

#define  SET_D0_PP     QSPI_PORT->OTYPER &= ~(1<<S_D0_PIN)
#define  SET_D1_PP     QSPI_PORT->OTYPER &= ~(1<<S_D1_PIN)
#define  SET_D2_PP     QSPI_PORT->OTYPER &= ~(1<<S_D2_PIN)
#define  SET_D3_PP     QSPI_PORT->OTYPER &= ~(1<<S_D3_PIN)

//------------------------------------------------------------
/*********     qspi protocol deefine     **********/
#define QPI_MODE_USE      1      // 0: using spi/dual/quad mode, 1: using QPI mode
     
#define SPI_MODE               0
#define DUAL_MODE              1
#define QUAD_MODE              2
#define QPI_MODE               4

#define QSPI_OK                0
#define QSPI_ERROR             1
#define QSPI_NOT_SUPPORTED     2
#define TIMING_OUT             3
     
#define FSR_BUSY               ((uint8_t)0x01)    /*!< busy */
#define FSR_WREN               ((uint8_t)0x02)    /*!< write enable */
#define FSR_QE                 ((uint8_t)0x02)    /*!< quad enable */
     
#define RESET_ENABLE           0x66
#define RESET_MEMORY           0x99
     
#define ENTER_QPI_MODE         0x38
#define EXIT_QPI_MODE          0xFF

/* Identification Operations */
#define SINGLE_READ_ID         0x90
#define DUAL_READ_ID           0x92
#define QUAD_READ_ID           0x94
#define READ_JEDEC_ID          0x9F

/* Read Operations */
#define READ_CMD               0x03
#define FAST_READ              0x0B
#define DUAL_OUT_FAST_READ     0x3B
#define DUAL_INOUT_FAST_READ   0xBB
#define QUAD_OUT_FAST_READ     0x6B
#define QUAD_INOUT_FAST_READ   0xEB

/* Write Operations */
#define WRITE_ENABLE           0x06
#define WRITE_DISABLE          0x04

/* Register Operations */
#define READ_STATUS_REG1       0x05
#define READ_STATUS_REG2       0x35
#define READ_STATUS_REG3       0x15

#define WRITE_STATUS_REG1      0x01
#define WRITE_STATUS_REG2      0x31
#define WRITE_STATUS_REG3      0x11

/* Program Operations */
#define PAGE_PROG_CMD          0x02
#define QUAD_IN_PAGE_PROG      0x32

/* Erase Operations */
#define SECTOR_ERASE           0x20
#define CHIP_ERASE             0xC7

#define PROG_ERASE_RESUME      0x7A
#define PROG_ERASE_SUSPEND     0x75

#define ENTER_4BYT_ADD_MODE    0xB7
#define EXIT_4BYT_ADD_MODE     0xE9

void delay_xns(void);
void soft_qspi_gpio_init(void);
void spi_start(void);
void spi_stop(void);
#ifdef STD_SPI_MODE
void spi_send_byte(uint8_t dat);
uint8_t spi_read_byte(void);
#else
uint8_t qspi_read_byte(uint8_t mod);
void qspi_send_byte(uint8_t dat, uint8_t mod);

void qspi_send_instruction(uint8_t cmd, uint8_t mod);
void qspi_send_address(uint32_t addr, uint8_t mod);
void qspi_send_altemate(uint8_t* altbuf, uint8_t size, uint8_t mode);
void qspi_send_dummy(uint8_t dum);
#endif
/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif
#endif /*__ spi_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
